#!/usr/bin/env node

// Filename: scripts/bundle-analyzer.js
const fs = require('fs');
const path = require('path');
const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generate = require('@babel/generator').default;
const t = require('@babel/types');

// 加载外部指纹库
function loadFingerprintDatabase() {
    try {
        const fingerprintPath = path.resolve(__dirname, '../npm-finger2.cjs');
        if (!fs.existsSync(fingerprintPath)) {
            console.error(`错误: 指纹库文件未找到: ${fingerprintPath}`);
            process.exit(1);
        }
        
        // 清除require缓存以确保获取最新版本
        delete require.cache[require.resolve(fingerprintPath)];
        const fingerprints = require(fingerprintPath);
        
        console.log(`✅ 成功加载指纹库，包含 ${Object.keys(fingerprints).length} 个NPM包指纹`);
        return fingerprints;
    } catch (error) {
        console.error(`❌ 加载指纹库失败: ${error.message}`);
        process.exit(1);
    }
}

class NpmPackageStripper {
    constructor(sourceCode, fingerprintDatabase) {
        this.sourceCode = sourceCode;
        this.NPM_FINGERPRINTS = fingerprintDatabase;

        try {
            console.log('正在解析AST，这可能需要一些时间...');
            this.ast = parser.parse(sourceCode, { sourceType: 'module', plugins: ['importMeta'] });
            console.log('✅ AST解析完成。');
        } catch (e) {
            console.error("AST解析失败。", e);
            throw e;
        }

        this.topLevelEntities = {
            modules: new Map(),
            functions: new Map(),
            classes: new Map(),
            variables: new Map(),
            expressions: [],
        };
        this.moduleFingerprints = new Map();
        this.entityDependencies = new Map();
        this.entryPoints = [];
    }

    run() {
        console.log('阶段 1: 编目所有顶层实体...');
        this.catalogTopLevelEntities();
        console.log(`  -> 发现 ${this.topLevelEntities.modules.size} 个模块, ${this.topLevelEntities.functions.size} 个函数, ${this.topLevelEntities.classes.size} 个类, ${this.topLevelEntities.variables.size} 个变量。`);

        console.log('\n阶段 2: 匹配NPM指纹...');
        this.identifyModulesByFingerprint();

        console.log('\n阶段 3: 构建全面的依赖关系图...');
        this.buildComprehensiveDependencyGraph();

        console.log('\n阶段 4: 查找应用入口点...');
        this.findEntryPoints();

        // 内存优化：拆分阶段5
        console.log('\n阶段 5a: 生成依赖地图 (基于原始结构)...');
        const dependencyMap = this.generateDependencyMap();

        console.log('\n阶段 5b: 原地修改AST以剪枝库...');
        this.pruneAstInPlace();

        console.log('\n阶段 5c: 从修改后的AST生成净化源码...');
        const { code: cleanedCode } = generate(this.ast, { comments: true, retainLines: false, concise: true });

        console.log('\n阶段 6: 生成最终报告...');
        const report = this.generateReport(dependencyMap);

        return { cleanedCode, report };
    }

    // --- 阶段 1: 编目所有顶层实体 (健壮版) ---
    catalogTopLevelEntities() {
        try {
            traverse(this.ast, {
                Program: (path) => {
                    path.get('body').forEach((nodePath, index) => {
                        try {
                            const node = nodePath.node;
                            if (t.isVariableDeclaration(node)) {
                                node.declarations.forEach(decl => {
                                    if (t.isIdentifier(decl.id)) {
                                        const name = decl.id.name;
                                        if (decl.init && t.isCallExpression(decl.init) && t.isIdentifier(decl.init.callee, { name: 'z' })) {
                                            this.topLevelEntities.modules.set(name, { path: nodePath, sourceSlice: this.sourceCode.substring(node.start, node.end) });
                                        } else {
                                            this.topLevelEntities.variables.set(name, { path: nodePath });
                                        }
                                    }
                                });
                            } else if (t.isFunctionDeclaration(node) && node.id) {
                                this.topLevelEntities.functions.set(node.id.name, { path: nodePath });
                            } else if (t.isClassDeclaration(node) && node.id) {
                                this.topLevelEntities.classes.set(node.id.name, { path: nodePath });
                            } else if (t.isExpressionStatement(node)) {
                                this.topLevelEntities.expressions.push({ path: nodePath });
                            }
                        } catch (nodeError) {
                            console.warn(`处理第 ${index} 个顶层节点时出错: ${nodeError.message}`);
                        }
                    });
                    path.stop();
                }
            });
        } catch (traverseError) {
            console.error(`编目顶层实体失败: ${traverseError.message}`);
            throw traverseError;
        }
    }

    // --- 阶段 2: 指纹匹配 ---
    identifyModulesByFingerprint() {
        for (const [moduleVarName, { sourceSlice, path }] of this.topLevelEntities.modules.entries()) {
            let bestMatch = { name: null, score: 0 };
            for (const [npmName, fingerprint] of Object.entries(this.NPM_FINGERPRINTS)) {
                let currentScore = 0;
                const signatureIdentifiers = new Set();
                path.traverse({
                    noScope: true,
                    Identifier(p) { if (p.key === 'key' || p.key === 'property') signatureIdentifiers.add(p.node.name); }
                });
                for (const fpString of fingerprint.strings) {
                    if (fpString instanceof RegExp) { if (sourceSlice.match(fpString)) currentScore += 15; }
                    else if (sourceSlice.includes(fpString)) { currentScore += 10; }
                }
                for (const fpExport of fingerprint.exports) {
                    if (signatureIdentifiers.has(fpExport)) currentScore += 5;
                }
                if (currentScore > bestMatch.score) bestMatch = { name: npmName, score: currentScore };
            }
            if (bestMatch.score > 20) {
                this.moduleFingerprints.set(moduleVarName, { identifiedAs: bestMatch.name, confidence: bestMatch.score });
                console.log(`  -> 模块 '${moduleVarName}' 高度疑似 '${bestMatch.name}' (置信度: ${bestMatch.score})`);
            }
        }
    }

    // --- 阶段 3: 全面依赖图构建 (终极增强版) ---
    buildComprehensiveDependencyGraph() {
        const allEntities = new Map([
            ...this.topLevelEntities.modules, ...this.topLevelEntities.functions,
            ...this.topLevelEntities.classes, ...this.topLevelEntities.variables,
        ]);
        const allEntityNames = new Set(allEntities.keys());

        const analyzeEntity = (name, { path }) => {
            const dependencies = new Set();
            const recordDependency = (depName) => {
                if (depName !== name && allEntityNames.has(depName)) dependencies.add(depName);
            };

            const visitor = {
                // 通用引用
                Identifier(p) { if (p.isReferencedIdentifier()) recordDependency(p.node.name); },

                // 函数/类调用
                CallExpression(p) { if (t.isIdentifier(p.node.callee)) recordDependency(p.node.callee.name); },
                NewExpression(p) { if (t.isIdentifier(p.node.callee)) recordDependency(p.node.callee.name); },

                // 成员访问
                MemberExpression(p) {
                    let object = p.get('object');
                    while (object.isMemberExpression()) object = object.get('object');
                    if (object.isIdentifier()) recordDependency(object.node.name);
                },

                // 赋值和表达式
                AssignmentExpression(p) { if (t.isIdentifier(p.node.right)) recordDependency(p.node.right.name); },
                ConditionalExpression(p) {
                    if (t.isIdentifier(p.node.consequent)) recordDependency(p.node.consequent.name);
                    if (t.isIdentifier(p.node.alternate)) recordDependency(p.node.alternate.name);
                },
                LogicalExpression(p) {
                    if (t.isIdentifier(p.node.left)) recordDependency(p.node.left.name);
                    if (t.isIdentifier(p.node.right)) recordDependency(p.node.right.name);
                },

                // 数据结构
                ObjectProperty(p) { if (t.isIdentifier(p.node.value)) recordDependency(p.node.value.name); },
                ArrayExpression(p) { p.get('elements').forEach(elemPath => { if (elemPath.isIdentifier()) recordDependency(elemPath.node.name); }); },
                SpreadElement(p) { if (t.isIdentifier(p.node.argument)) recordDependency(p.node.argument.name); },
                TemplateLiteral(p) { p.get('expressions').forEach(exprPath => { if (exprPath.isIdentifier()) recordDependency(exprPath.node.name); }); },

                // 控制流
                ReturnStatement(p) { if (p.node.argument && t.isIdentifier(p.node.argument)) recordDependency(p.node.argument.name); },
                ThrowStatement(p) { if (t.isIdentifier(p.node.argument)) recordDependency(p.node.argument.name); },
                SwitchStatement(p) { if (t.isIdentifier(p.node.discriminant)) recordDependency(p.node.discriminant.name); },
                SwitchCase(p) { if (p.node.test && t.isIdentifier(p.node.test)) recordDependency(p.node.test.name); },
                ForOfStatement(p) { if (t.isIdentifier(p.node.right)) recordDependency(p.node.right.name); },
                ForInStatement(p) { if (t.isIdentifier(p.node.right)) recordDependency(p.node.right.name); },

                // 类和模块系统
                ClassDeclaration(p) { if (p.node.superClass && t.isIdentifier(p.node.superClass)) recordDependency(p.node.superClass.name); },
                ClassExpression(p) { if (p.node.superClass && t.isIdentifier(p.node.superClass)) recordDependency(p.node.superClass.name); },
                ExportDefaultDeclaration(p) { if (t.isIdentifier(p.node.declaration)) recordDependency(p.node.declaration.name); }
            };

            // 处理函数参数默认值
            const processParams = (nodePath) => {
                try {
                    if (nodePath.node && nodePath.node.params) {
                        nodePath.node.params.forEach(param => {
                            if (t.isAssignmentPattern(param) && t.isIdentifier(param.right)) {
                                recordDependency(param.right.name);
                            }
                        });
                    }
                } catch (e) {
                    // 忽略参数处理错误
                    console.log('processParams error ===>', e);
                }
            };

            if (path.isFunction() || path.isClass()) {
                processParams(path);
            }

            path.traverse(visitor);
            this.entityDependencies.set(name, Array.from(dependencies));
        };

        for (const [name, data] of allEntities.entries()) analyzeEntity(name, data);
        this.topLevelEntities.expressions.forEach((data, index) => {
            const name = `_top_level_expression_${index}_`;
            analyzeEntity(name, data);
        });
    }

    // --- 阶段 4: 查找入口点 (增强版) ---
    findEntryPoints() {
        const allDeclaredEntities = new Set([...this.topLevelEntities.modules.keys(), ...this.topLevelEntities.functions.keys(), ...this.topLevelEntities.classes.keys(), ...this.topLevelEntities.variables.keys()]);
        const allDependencies = new Set();
        for (const deps of this.entityDependencies.values()) { deps.forEach(dep => allDependencies.add(dep)); }

        const expressionEntries = [];
        this.topLevelEntities.expressions.forEach((_, index) => {
            const expressionDeps = this.entityDependencies.get(`_top_level_expression_${index}_`);
            if (expressionDeps && expressionDeps.length > 0) expressionEntries.push(...expressionDeps);
        });
        if (expressionEntries.length > 0) {
            const uniqueEntries = [...new Set(expressionEntries)].filter(name => !this.moduleFingerprints.has(name));
            console.log(`  -> 发现 ${uniqueEntries.length} 个由顶层表达式驱动的入口点: ${uniqueEntries.join(', ')}`);
            this.entryPoints = uniqueEntries;
            return;
        }

        const unreferencedEntries = [...allDeclaredEntities].filter(name => !allDependencies.has(name));
        const filteredEntryPoints = unreferencedEntries.filter(name => !this.moduleFingerprints.has(name));
        if (filteredEntryPoints.length > 0) {
            console.log(`  -> 发现 ${filteredEntryPoints.length} 个潜在入口点 (未被依赖的实体): ${filteredEntryPoints.join(', ')}`);
            this.entryPoints = filteredEntryPoints;
            return;
        }

        console.warn("  -> 警告: 使用备用入口点识别策略 (所有非库模块)。");
        this.entryPoints = [...this.topLevelEntities.modules.keys()].filter(name => !this.moduleFingerprints.has(name));
    }

    // --- 新增: 阶段5a - 仅生成依赖地图 ---
    generateDependencyMap() {
        const dependencyMap = {};
        for (const entry of this.entryPoints) {
            dependencyMap[entry] = this.buildTreeNode(entry, new Set());
        }
        return dependencyMap;
    }

    // --- 新增: 阶段5b - 原地修改AST ---
    pruneAstInPlace() {
        const self = this;
        const libraryModuleNames = new Set(this.moduleFingerprints.keys());

        traverse(this.ast, {
            Program(path) {
                path.get('body').forEach(nodePath => {
                    if (t.isVariableDeclaration(nodePath.node)) {
                        nodePath.get('declarations').forEach(declPath => {
                            try {
                                const idNode = declPath.node.id;

                                // 1. 如果是库模块的定义，剪枝处理
                                if (t.isIdentifier(idNode) &&
                                    declPath.node.init &&
                                    t.isCallExpression(declPath.node.init) &&
                                    t.isIdentifier(declPath.node.init.callee, { name: 'z' }) &&
                                    libraryModuleNames.has(idNode.name)) {

                                    const libInfo = self.moduleFingerprints.get(idNode.name);
                                    self.pruneLibraryModule(declPath.node, libInfo, idNode.name);
                                    return;
                                }

                                // 2. 深入业务逻辑，替换对库的调用
                                declPath.traverse({
                                    CallExpression(innerPath) {
                                        const callee = innerPath.get('callee');
                                        if (callee.isIdentifier()) {
                                            const libInfo = self.moduleFingerprints.get(callee.node.name);
                                            if (libInfo) {
                                                console.log(`🔧 替换库调用: ${callee.node.name}() → require('${libInfo.identifiedAs}')`);
                                                innerPath.replaceWith(
                                                    t.callExpression(t.identifier('require'), [t.stringLiteral(libInfo.identifiedAs)])
                                                );
                                                t.addComment(innerPath.node, "leading", ` [Replaced] `, true);
                                            }
                                        }
                                    }
                                });
                            } catch (declError) {
                                console.warn('处理声明时出错:', declError.message);
                            }
                        });

                        // 如果一个var/let/const语句的所有声明符都被移除了，则移除整个语句
                        if (nodePath.node.declarations.length === 0) {
                            nodePath.remove();
                        }
                    }
                });
            }
        });
    }

    // 剪枝处理库模块：保持结构但最小化内容
    pruneLibraryModule(declarator, libInfo, moduleName) {
        const originalFunction = declarator.init.arguments[0];

        if (t.isFunctionExpression(originalFunction)) {
            // 计算原始大小
            const originalSize = this.calculateModuleSize(originalFunction);

            // 创建最小化的注释
            const prunedComment = ` ${libInfo.identifiedAs} (confidence: ${libInfo.confidence}) - ${originalSize} lines pruned `;

            // 替换函数体为空的块语句，只包含注释
            originalFunction.body = t.blockStatement([]);

            // 添加注释
            t.addComment(originalFunction.body, 'inner', prunedComment, true);

            console.log(`🗑️  剪枝库模块: ${moduleName} → ${libInfo.identifiedAs} (${originalSize} lines → comment)`);
        }
    }

    // 计算模块原始大小
    calculateModuleSize(functionNode) {
        if (functionNode.start && functionNode.end) {
            const content = this.sourceCode.substring(functionNode.start, functionNode.end);
            return content.split('\n').length;
        }
        return 'unknown';
    }

    // --- 优化: `buildTreeNode` 使用复用Set ---
    buildTreeNode(entityName, visited) {
        const libInfo = this.moduleFingerprints.get(entityName);
        if (libInfo) return { 'type': 'library', 'package': libInfo.identifiedAs, 'status': 'pruned' };
        if (visited.has(entityName)) return { 'status': `circular_dependency_to: ${entityName}` };

        visited.add(entityName); // 在进入递归前添加

        const dependencies = this.entityDependencies.get(entityName) || [];
        const node = {
            type: this.topLevelEntities.modules.has(entityName) ? 'module' : 'function/class/variable',
            dependencies: {},
            status: dependencies.length > 0 ? 'application_logic_node' : 'application_logic_leaf'
        };
        for (const depName of dependencies) {
            // 传递同一个set，避免创建新的Set对象
            node.dependencies[depName] = this.buildTreeNode(depName, visited);
        }

        visited.delete(entityName); // 在当前分支返回后移除，实现回溯
        return node;
    }

    // --- 阶段 6: 生成报告 ---
    generateReport(dependencyMap) {
        const businessLogicModules = [...this.topLevelEntities.modules.keys()].filter(name => !this.moduleFingerprints.has(name));
        const identifiedLibraries = new Map();
        for (const [moduleVarName, lib] of this.moduleFingerprints.entries()) {
            if (!identifiedLibraries.has(lib.identifiedAs)) identifiedLibraries.set(lib.identifiedAs, []);
            identifiedLibraries.get(lib.identifiedAs).push(moduleVarName);
        }
        return {
            summary: {
                totalModules: this.topLevelEntities.modules.size,
                totalFunctions: this.topLevelEntities.functions.size,
                totalClasses: this.topLevelEntities.classes.size,
                identifiedLibrariesCount: identifiedLibraries.size,
                businessLogicModulesCount: businessLogicModules.length,
                entryPoints: this.entryPoints,
            },
            dependencyMap,
            identifiedLibraries: Object.fromEntries(identifiedLibraries),
            applicationEntities: {
                modules: businessLogicModules,
                functions: [...this.topLevelEntities.functions.keys()],
                classes: [...this.topLevelEntities.classes.keys()],
                variables: [...this.topLevelEntities.variables.keys()],
            },
        };
    }
}

// --- 主执行逻辑 ---
function main() {
    console.log("分析器启动...");

    // 内存优化提示
    console.warn("\n💡 提示: 如果遇到内存溢出 (heap out of memory) 错误, 请尝试使用以下命令增加Node.js可用内存:");
    console.warn("   node --max-old-space-size=8192 scripts/bundle-analyzer.js <your-file.js>\n");

    const args = process.argv.slice(2);
    if (args.length !== 1) {
        console.error("用法: node bundle-analyzer.js <源文件路径.js>");
        process.exit(1);
    }

    const sourceFilePath = path.resolve(args[0]);
    if (!fs.existsSync(sourceFilePath)) {
        console.error(`错误: 文件未找到于 ${sourceFilePath}`);
        process.exit(1);
    }

    // 确保输出目录存在
    const outputDir = path.resolve(__dirname, '../output');
    if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
        console.log(`✅ 创建输出目录: ${outputDir}`);
    }

    console.log(`正在读取源文件: ${sourceFilePath}\n`);
    const sourceCode = fs.readFileSync(sourceFilePath, 'utf8');

    try {
        const fingerprintDatabase = loadFingerprintDatabase();
        const stripper = new NpmPackageStripper(sourceCode, fingerprintDatabase);
        const result = stripper.run();

        if (result) {
            const { cleanedCode, report } = result;
            const baseName = path.basename(sourceFilePath, '.js');

            const cleanedFilePath = path.join(outputDir, `${baseName}.pruned.js`);
            const reportFilePath = path.join(outputDir, `${baseName}.report.json`);

            fs.writeFileSync(cleanedFilePath, cleanedCode, 'utf8');
            fs.writeFileSync(reportFilePath, JSON.stringify(report, null, 2), 'utf8');

            console.log('\n✅ 处理完成!');
            console.log('='.repeat(50));
            console.log(`📄 净化后的代码: ${cleanedFilePath}`);
            console.log(`📊 分析报告: ${reportFilePath}`);
        }
    } catch(error) {
        console.error("\n❌ 处理过程中发生严重错误:", error.stack);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

// --- 主执行逻辑 ---
function main() {
    const args = process.argv.slice(2);
    if (args.length !== 1) {
        console.error("用法: node bundle-analyzer.js <源文件路径.js>");
        process.exit(1);
    }

    const sourceFilePath = path.resolve(args[0]);
    if (!fs.existsSync(sourceFilePath)) {
        console.error(`错误: 文件未找到于 ${sourceFilePath}`);
        process.exit(1);
    }

    // 确保输出目录存在
    const outputDir = path.resolve(__dirname, '../output');
    if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
        console.log(`✅ 创建输出目录: ${outputDir}`);
    }

    console.log(`正在读取源文件: ${sourceFilePath}\n`);
    const sourceCode = fs.readFileSync(sourceFilePath, 'utf8');

    try {
        // 加载指纹库
        const fingerprintDatabase = loadFingerprintDatabase();

        // 创建分析器实例
        const stripper = new NpmPackageStripper(sourceCode, fingerprintDatabase);
        const result = stripper.run();

        if (result) {
            const { cleanedCode, report } = result;
            const baseName = path.basename(sourceFilePath, '.js');

            // 输出到 output 目录
            const cleanedFilePath = path.join(outputDir, `${baseName}.pruned.js`);
            const reportFilePath = path.join(outputDir, `${baseName}.report.json`);

            fs.writeFileSync(cleanedFilePath, cleanedCode, 'utf8');
            fs.writeFileSync(reportFilePath, JSON.stringify(report, null, 2), 'utf8');

            console.log('\n✅ 处理完成!');
            console.log('='.repeat(50));
            console.log(`📄 净化后的代码: ${cleanedFilePath}`);
            console.log(`📊 分析报告: ${reportFilePath}`);
            console.log(`📈 统计信息:`);
            console.log(`   - 总模块数: ${report.summary.totalModules}`);
            console.log(`   - 识别的库: ${report.summary.identifiedLibrariesCount}`);
            console.log(`   - 业务模块: ${report.summary.businessLogicModulesCount}`);
            console.log(`   - 入口点: ${report.summary.entryPoints.length}`);
        }
    } catch(error) {
        console.error("\n❌ 处理过程中发生严重错误:", error.stack);
        process.exit(1);
    }
}

// 如果直接运行此脚本，则执行主函数
if (require.main === module) {
    main();
}

// --- 主执行逻辑 ---
function main() {
    const args = process.argv.slice(2);
    if (args.length !== 1) {
        console.error("用法: node bundle-analyzer.js <源文件路径.js>");
        process.exit(1);
    }

    const sourceFilePath = path.resolve(args[0]);
    if (!fs.existsSync(sourceFilePath)) {
        console.error(`错误: 文件未找到于 ${sourceFilePath}`);
        process.exit(1);
    }

    // 确保输出目录存在
    const outputDir = path.resolve(__dirname, '../output');
    if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
        console.log(`✅ 创建输出目录: ${outputDir}`);
    }

    console.log(`正在读取源文件: ${sourceFilePath}\n`);
    const sourceCode = fs.readFileSync(sourceFilePath, 'utf8');

    try {
        // 加载指纹库
        const fingerprintDatabase = loadFingerprintDatabase();

        // 创建分析器实例
        const stripper = new NpmPackageStripper(sourceCode, fingerprintDatabase);
        const result = stripper.run();

        if (result) {
            const { cleanedCode, report } = result;
            const baseName = path.basename(sourceFilePath, '.js');

            // 输出到 output 目录
            const cleanedFilePath = path.join(outputDir, `${baseName}.pruned.js`);
            const reportFilePath = path.join(outputDir, `${baseName}.report.json`);

            fs.writeFileSync(cleanedFilePath, cleanedCode, 'utf8');
            fs.writeFileSync(reportFilePath, JSON.stringify(report, null, 2), 'utf8');

            console.log('\n✅ 处理完成!');
            console.log('='.repeat(50));
            console.log(`📄 净化后的代码: ${cleanedFilePath}`);
            console.log(`📊 分析报告: ${reportFilePath}`);
            console.log(`📈 统计信息:`);
            console.log(`   - 总模块数: ${report.summary.totalModules}`);
            console.log(`   - 识别的库: ${report.summary.identifiedLibrariesCount}`);
            console.log(`   - 业务模块: ${report.summary.businessLogicModulesCount}`);
            console.log(`   - 入口点: ${report.summary.entryPoints.length}`);
        }
    } catch(error) {
        console.error("\n❌ 处理过程中发生严重错误:", error.stack);
        process.exit(1);
    }
}

// 如果直接运行此脚本，则执行主函数
if (require.main === module) {
    main();
}
